# Email Setup for Lead Capture Form

## Overview
The website now includes a lead capture form titled "Estimate price for your site" that collects visitor information and emails it to you automatically using Resend.

## Features
- **Form Fields**: Name, Email, Company, Phone, Project Type, Budget Range, Timeline, Project Description
- **Email Notifications**: You receive a beautifully formatted email with all form details
- **Auto-Response**: Visitors receive a professional confirmation email
- **Validation**: Client and server-side form validation
- **Responsive Design**: Works on all devices
- **Professional Templates**: HTML-formatted emails with your branding

## Email Configuration

### Step 1: Resend Setup (Already Done!)
Your Resend API key is already configured:
- API Key: `re_aj8R9TYe_NYLZzEpsDbPsJ1gAJtJAKCkD`
- Service: Resend (much more reliable than Gmail)

### Step 2: Environment Variables (Already Set!)
Your `.env.local` file is already configured with:

```env
RESEND_API_KEY=re_aj8R9TYe_NYLZzEpsDbPsJ1gAJtJAKCkD
```

**Benefits of Resend**:
- Higher delivery rates than Gmail
- Professional email templates
- Better spam protection
- Detailed analytics
- No authentication issues

### Step 3: Test the Form
1. Start your development server: `npm run dev`
2. Navigate to the estimate form section
3. Fill out and submit a test form
4. Check your email for the notification

## Form Location
The estimate form is accessible via:
- Navigation menu: "Get Quote"
- Hero section: "Get Free Quote" button
- Floating action button: "Get Free Quote"
- Mobile menu: "Get Free Quote"
- Direct link: `#estimate`

## Email Templates

### Notification Email (to you)
- Subject: "New Website Estimate Request from [Name]"
- Contains all form data in a readable format
- Includes visitor's contact information and project details

### Confirmation Email (to visitor)
- Subject: "Thank you for your website estimate request"
- Professional acknowledgment message
- Sets expectation for 24-hour response time
- Includes your contact information

## Troubleshooting

### Common Issues
1. **"API key invalid"**: Verify your Resend API key is correct
2. **"Network error"**: Check your internet connection
3. **Form not submitting**: Check browser console for JavaScript errors
4. **Emails not arriving**: Check spam folders (Resend has excellent deliverability)

### Testing Tips
- Use a real email address when testing
- Resend emails typically arrive within seconds
- Check the Resend dashboard for delivery status
- Verify environment variables are loaded correctly

## Security Notes
- Form includes CSRF protection via Next.js
- Email validation on both client and server
- Rate limiting can be added if needed
- Environment variables keep credentials secure

## Customization
You can modify the form by editing:
- `src/components/EstimateForm.tsx` - Form component
- `src/app/api/estimate/route.ts` - Resend email handling logic
- HTML email templates in the API route file
- Add more fields or change styling as needed

## Resend Features
- Professional HTML email templates
- Automatic bounce and complaint handling
- Email analytics and tracking
- High deliverability rates
- Easy integration with Next.js
