"use client";

import { useState, useEffect } from 'react';

export default function MobileMenu() {
  const [isOpen, setIsOpen] = useState(false);

  const toggleMenu = () => setIsOpen(!isOpen);
  const closeMenu = () => setIsOpen(false);

  // Prevent scrolling when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  return (
    <div className="block sm:hidden">
      {/* Mobile menu button */}
      <button
        className="flex items-center justify-center w-10 h-10 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
        onClick={toggleMenu}
        aria-label="Open menu"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>

      {/* Mobile menu overlay with animation */}
      <div
        className={`fixed inset-0 bg-black z-50 transform transition-all duration-300 ease-in-out ${isOpen ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0 pointer-events-none'}`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex justify-between items-center p-5 border-b border-gray-800 bg-black">
            <a href="#" className="flex items-center gap-3 hover:opacity-90 transition-opacity" onClick={closeMenu}>
              <img
                src="/favicon_white/android-chrome-512x512.png"
                alt="Timoor Nurzhanov Logo"
                className="w-8 h-8"
              />
              <span className="font-bold text-xl text-white">Timoor Nurzhanov</span>
            </a>
            <button
              className="w-10 h-10 flex items-center justify-center text-white hover:text-blue-400 transition-colors rounded-full hover:bg-gray-800"
              onClick={closeMenu}
              aria-label="Close menu"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex flex-col py-6 px-6 bg-black">
            <div className="mb-2 px-3">
              <span className="text-sm font-semibold px-4 py-1.5 rounded-full bg-blue-900/50 text-blue-400 inline-block mb-4">
                Web Developer & Designer
              </span>
            </div>

            <a
              href="#about"
              className="py-4 px-3 text-xl font-bold text-white hover:text-blue-400 transition-colors border-b border-gray-800 flex items-center"
              onClick={closeMenu}
            >
              <svg className="w-5 h-5 mr-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              About
            </a>
            <a
              href="#services"
              className="py-4 px-3 text-xl font-bold text-white hover:text-blue-400 transition-colors border-b border-gray-800 flex items-center"
              onClick={closeMenu}
            >
              <svg className="w-5 h-5 mr-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              Services
            </a>
            <a
              href="#work"
              className="py-4 px-3 text-xl font-bold text-white hover:text-blue-400 transition-colors border-b border-gray-800 flex items-center"
              onClick={closeMenu}
            >
              <svg className="w-5 h-5 mr-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
              Work
            </a>
            <a
              href="#estimate"
              className="py-4 px-3 text-xl font-bold text-white hover:text-blue-400 transition-colors border-b border-gray-800 flex items-center"
              onClick={closeMenu}
            >
              <svg className="w-5 h-5 mr-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
              </svg>
              Get Quote
            </a>
            <a
              href="#contact"
              className="py-4 px-3 text-xl font-bold text-white hover:text-blue-400 transition-colors flex items-center"
              onClick={closeMenu}
            >
              <svg className="w-5 h-5 mr-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              Contact
            </a>
          </nav>

          {/* Hero text */}
          <div className="px-6 py-4 bg-black">
            <h1 className="text-3xl font-bold mb-3 text-white">
              High-Converting <span className="text-blue-400">Business Websites</span>
            </h1>
            <p className="text-base text-gray-300 mb-6">
              I craft strategic, high-performance websites that help businesses attract more customers, increase conversions, and grow their online presence.
            </p>
            <div className="flex flex-wrap gap-3 mb-6">
              <a href="#estimate" className="px-5 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors shadow-sm text-sm">
                Get Free Quote
              </a>
              <a href="#work" className="px-5 py-2.5 bg-gray-800 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors text-sm">
                View My Work
              </a>
            </div>
          </div>

          {/* Stats */}
          <div className="px-6 py-4 bg-black">
            <div className="flex items-center gap-4 mb-2">
              <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-white font-medium">5+ Years Experience</span>
            </div>
            <div className="flex items-center gap-4">
              <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-white font-medium">50+ Projects Delivered</span>
            </div>
          </div>

          {/* Footer */}
          <div className="mt-auto p-6 text-center text-gray-400 text-sm border-t border-gray-800 bg-black">
            <p>&copy; {new Date().getFullYear()} Timoor Nurzhanov</p>
          </div>
        </div>
      </div>
    </div>
  );
}
