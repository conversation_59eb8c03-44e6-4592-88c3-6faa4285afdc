import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const { name, email, company, phone, projectType, budget, description, timeline } = body;
    
    if (!name || !email) {
      return NextResponse.json(
        { error: 'Name and email are required' },
        { status: 400 }
      );
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Please provide a valid email address' },
        { status: 400 }
      );
    }

    // Create transporter
    const transporter = nodemailer.createTransporter({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    // Email content
    const emailContent = `
      New Website Estimate Request
      
      Contact Information:
      - Name: ${name}
      - Email: ${email}
      - Company: ${company || 'Not provided'}
      - Phone: ${phone || 'Not provided'}
      
      Project Details:
      - Project Type: ${projectType || 'Not specified'}
      - Budget Range: ${budget || 'Not specified'}
      - Timeline: ${timeline || 'Not specified'}
      
      Project Description:
      ${description || 'No description provided'}
      
      ---
      This email was sent from the website estimate form at timodev.com
    `;

    // Send email to you
    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: '<EMAIL>',
      subject: `New Website Estimate Request from ${name}`,
      text: emailContent,
      html: emailContent.replace(/\n/g, '<br>'),
    });

    // Send confirmation email to the client
    const confirmationContent = `
      Hi ${name},
      
      Thank you for your interest in working with me! I've received your website estimate request and will get back to you within 24 hours.
      
      Here's a summary of what you submitted:
      - Project Type: ${projectType || 'Not specified'}
      - Budget Range: ${budget || 'Not specified'}
      - Timeline: ${timeline || 'Not specified'}
      
      I look forward to discussing your project in detail.
      
      Best regards,
      Timoor Nurzhanov
      Web Developer & Designer
      <EMAIL>
      https://timodev.com
    `;

    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: email,
      subject: 'Thank you for your website estimate request',
      text: confirmationContent,
      html: confirmationContent.replace(/\n/g, '<br>'),
    });

    return NextResponse.json(
      { message: 'Estimate request sent successfully!' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      { error: 'Failed to send estimate request. Please try again.' },
      { status: 500 }
    );
  }
}
