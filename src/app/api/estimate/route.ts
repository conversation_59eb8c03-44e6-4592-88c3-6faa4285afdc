import { NextRequest, NextResponse } from 'next/server';
import { Resend } from 'resend';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const { name, email, company, phone, projectType, budget, description, timeline } = body;
    
    if (!name || !email) {
      return NextResponse.json(
        { error: 'Name and email are required' },
        { status: 400 }
      );
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Please provide a valid email address' },
        { status: 400 }
      );
    }

    // Initialize Resend
    const resend = new Resend(process.env.RESEND_API_KEY);

    // Send notification email to you
    await resend.emails.send({
      from: 'Website Form <<EMAIL>>',
      to: 'timoor.n<PERSON><PERSON><PERSON><PERSON>@gmail.com',
      subject: `New Website Estimate Request from ${name}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #2563eb; border-bottom: 2px solid #2563eb; padding-bottom: 10px;">
            New Website Estimate Request
          </h2>

          <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1e293b; margin-top: 0;">Contact Information</h3>
            <p><strong>Name:</strong> ${name}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Company:</strong> ${company || 'Not provided'}</p>
            <p><strong>Phone:</strong> ${phone || 'Not provided'}</p>
          </div>

          <div style="background-color: #f1f5f9; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1e293b; margin-top: 0;">Project Details</h3>
            <p><strong>Project Type:</strong> ${projectType || 'Not specified'}</p>
            <p><strong>Budget Range:</strong> ${budget || 'Not specified'}</p>
            <p><strong>Timeline:</strong> ${timeline || 'Not specified'}</p>
          </div>

          <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1e293b; margin-top: 0;">Project Description</h3>
            <p style="white-space: pre-wrap;">${description || 'No description provided'}</p>
          </div>

          <hr style="margin: 30px 0; border: none; border-top: 1px solid #e2e8f0;">
          <p style="color: #64748b; font-size: 14px; text-align: center;">
            This email was sent from the website estimate form at timodev.com
          </p>
        </div>
      `,
    });

    // Send confirmation email to the client
    await resend.emails.send({
      from: 'Timoor Nurzhanov <<EMAIL>>',
      to: email,
      subject: 'Thank you for your website estimate request',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin-bottom: 10px;">Thank You!</h1>
            <p style="color: #64748b; font-size: 18px;">Your estimate request has been received</p>
          </div>

          <div style="background-color: #f8fafc; padding: 25px; border-radius: 8px; margin: 20px 0;">
            <p>Hi ${name},</p>

            <p>Thank you for your interest in working with me! I've received your website estimate request and will get back to you within 24 hours.</p>

            <h3 style="color: #1e293b; margin-top: 25px;">Here's a summary of what you submitted:</h3>
            <ul style="color: #475569;">
              <li><strong>Project Type:</strong> ${projectType || 'Not specified'}</li>
              <li><strong>Budget Range:</strong> ${budget || 'Not specified'}</li>
              <li><strong>Timeline:</strong> ${timeline || 'Not specified'}</li>
            </ul>

            <p style="margin-top: 25px;">I look forward to discussing your project in detail and helping you create a high-converting website that drives results for your business.</p>
          </div>

          <div style="background-color: #2563eb; color: white; padding: 20px; border-radius: 8px; text-align: center; margin: 30px 0;">
            <h3 style="margin: 0 0 10px 0;">Timoor Nurzhanov</h3>
            <p style="margin: 5px 0;">Web Developer & Designer</p>
            <p style="margin: 5px 0;">
              <a href="mailto:<EMAIL>" style="color: #bfdbfe;"><EMAIL></a>
            </p>
            <p style="margin: 5px 0;">
              <a href="https://timodev.com" style="color: #bfdbfe;">timodev.com</a>
            </p>
          </div>

          <p style="color: #64748b; font-size: 14px; text-align: center; margin-top: 30px;">
            This is an automated confirmation email. Please do not reply to this email.
          </p>
        </div>
      `,
    });

    return NextResponse.json(
      { message: 'Estimate request sent successfully!' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      { error: 'Failed to send estimate request. Please try again.' },
      { status: 500 }
    );
  }
}
